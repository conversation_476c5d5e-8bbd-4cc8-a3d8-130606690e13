import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Avenir Next Rounded', 'Arial', 'Helvetica', 'sans-serif'],
        avenir: ['Avenir Next Rounded', 'Arial', 'Helvetica', 'sans-serif'],
      },
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        'primary-background': 'var(--primary-background)',
        'dark-foreground': 'var(--dark-foreground)',
        destructive: 'var(--destructive)',
        primary: 'var(--primary)',
        'primary-foreground': 'var(--primary-foreground)',
        secondary: 'var(--secondary)',
        'secondary-foreground': 'var(--secondary-foreground)',
        success: 'vat(--success)',
        muted: 'var(--muted)',
        'muted-foreground': 'var(--muted-foreground)',
        accent: 'var(--accent)',
        'accent-foreground': 'var(--accent-foreground)',
        border: 'var(--border)',
        input: 'var(--input)',
        ring: 'var(--ring)',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      boxShadow: {
        metal:
          '0 0.375rem 0.8125rem rgba(0, 0, 0, 0.29), 0 1.5rem 1.5rem 0rem rgba(0, 0, 0, 0.26), 0 3.375rem 2rem 0rem rgba(0, 0, 0, 0.15), 0 6rem 2.375rem 0rem rgba(0, 0, 0, 0.04), 0 9.375rem 2.625rem 0rem rgba(0, 0, 0, 0.01)',
      },
    },
  },
  plugins: [],
};

export default config;
