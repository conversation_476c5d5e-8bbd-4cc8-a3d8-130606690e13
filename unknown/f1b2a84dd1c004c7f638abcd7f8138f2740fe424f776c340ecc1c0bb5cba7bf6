'use client';

import { Button, Input } from '@/components/ui';
import Image from 'next/image';
import React, { useState } from 'react';
import {
  closeIcon,
  emailIcon,
  passwordIcon,
  personIcon,
} from '@/assets/images/icons';
import { popUpTypes } from '@/types';

type SignUpProps = {
  toggleOnlyPopUp: (key: popUpTypes) => void;
};

export default function SignUp({ toggleOnlyPopUp }: SignUpProps) {
  const [email, setEmail] = useState('');
  const [confirmEmail, setConfirmEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error] = useState('');

  return (
    <div
      className="fixed inset-0 z-50 min-h-screen w-full bg-white/30 backdrop-blur-sm"
      onClick={() => toggleOnlyPopUp('signUp')}
    >
      {/* Modal Card */}
      <div
        onClick={(e) => e.stopPropagation()}
        className="relative bg-white bg-opacity-90 rounded-3xl px-[1.75rem] py-[1.1875rem] mx-auto mt-32 w-full max-w-[21rem] flex flex-col items-center justify-center border-[none] text-[1.25rem] [transition:all_0.2s_ease] z-10 font-bold font-['Avenir_Next_Rounded',_sans-serif] [text-shadow:0_.0313rem_.0625rem_rgba(0,_0,_0,_0.12)] whitespace-nowrap border-silver"
      >
        <div className="absolute -top-2 -right-2">
          {/* Close Button */}
          <Button
            variant="light"
            size="icon"
            className="rounded-full hover:bg-gray-200 transition !shadow-[inset_0px_3.78px_3.78px_rgba(0,0,0,0.1),_inset_-7.56px_3.78px_4.73px_rgba(0,0,0,0.05),_0px_0px_2.84px_rgba(0,0,0,1)]"
            onClick={() => toggleOnlyPopUp('signUp')}
          >
            <Image src={closeIcon} alt="Close" width={20} height={20} />
          </Button>
        </div>

        <div className="flex flex-col justify-center items-center w-full gap-[1.1875rem]">
          <div className="flex flex-col justify-center items-center gap-1">
            {/* Icon */}
            <Image src={personIcon} alt="User" width={48} height={48} />
            {/* Title */}
            <h2 className="text-2xl !text-dark-foreground font-bold text-center">
              Create Account
            </h2>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-destructive text-sm text-center font-medium">
              {error}
            </div>
          )}

          {/* Email Input */}
          <div className="w-full">
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email"
              leftIcon={
                <Image src={emailIcon} alt="Email" width={24} height={24} />
              }
              className="font-[500] !text-dark-foreground"
              containerClassName=""
              autoComplete="email"
            />
          </div>
          {/* Confirm Email Input */}
          <div className="w-full">
            <Input
              type="email"
              value={confirmEmail}
              onChange={(e) => setConfirmEmail(e.target.value)}
              placeholder="Confirm Email"
              leftIcon={
                <Image
                  src={emailIcon}
                  alt="Confirm Email"
                  width={24}
                  height={24}
                />
              }
              className="font-[500] !text-dark-foreground"
              containerClassName=""
              autoComplete="email"
            />
          </div>
          {/* Password Input */}
          <div className="w-full">
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
              leftIcon={
                <Image
                  src={passwordIcon}
                  alt="Password"
                  width={24}
                  height={24}
                />
              }
              className="font-[500] !text-dark-foreground"
              containerClassName=""
              autoComplete="new-password"
            />
          </div>
          {/* Create Account Button */}
          <Button
            variant="dark"
            disabled={true}
            size="md"
            className="shadow-md"
          >
            Create Account
          </Button>
          {/* Links */}
          <div className="w-full flex flex-col items-center text-sm gap-[1.1875rem] text-gray-700 text-wrap">
            <div className="text-center font-[600] text-[.9375rem]">
              Already have an account?{' '}
              <a
                className="font-bold cursor-pointer hover:underline"
                onClick={() => toggleOnlyPopUp('signIn')}
              >
                Sign in
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
