@import 'tailwindcss';

/* Performance optimizations */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

/* Optimized font declarations */
@font-face {
  font-family: 'Avenir Next Rounded';
  src: url('../assets/fonts/Avenir Next Rounded/Avenir Next Rounded/Avenir Next Rounded.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Avenir Next Rounded';
  src: url('../assets/fonts/Avenir Next Rounded/Avenir Next Rounded/Avenir Next Rounded_Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Avenir Next Rounded';
  src: url('../assets/fonts/Avenir Next Rounded/Avenir Next Rounded/Avenir Next Rounded_Demi.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Avenir Next Rounded';
  src: url('../assets/fonts/Avenir Next Rounded/Avenir Next Rounded/Avenir Next Rounded_Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* CSS Custom Properties */
:root {
  /* Color System */
  --background: #ffffff;
  --foreground: #171717;
  --primary-background: #3a256f;
  --dark-foreground: #404040;
  --destructive: #f62c2c;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --success: #06df3b;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #6366f1;
  
  /* Typography */
  --font-sans: 'Avenir Next Rounded', Arial, Helvetica, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  
  /* Spacing */
  --radius: 0.5rem;
  --border-radius: 1rem;
  
  /* Shadows */
  --shadow: 0 .0625rem .1875rem 0 rgb(0 0 0 / 0.1), 0 .0625rem .125rem -0.0625rem rgb(0 0 0 / 0.1);
  --shadow-md: 0 .25rem .375rem -0.0625rem rgb(0 0 0 / 0.1), 0 .125rem .25rem -0.125rem rgb(0 0 0 / 0.1);
  --shadow-lg: 0 .625rem .9375rem -0.1875rem rgb(0 0 0 / 0.1), 0 .25rem .375rem -0.25rem rgb(0 0 0 / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary-background: var(--primary-background);
  --color-dark-foreground: var(--dark-foreground);
  --color-destructive: var(--destructive);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-success: var(--success);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ffffff;
    --primary-background: #3a256f;
    --dark-foreground: #404040;
    --destructive: #f62c2c;
    --primary: #1e094e;
    --primary-foreground: #ffffff;
    --secondary: #1e094e;
    --secondary-foreground: #f8fafc;
    --success: #06df3b;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f8fafc;
    --border: #334155;
    --input: #1e293b;
    --ring: #6366f1;
  }
}

/* Base styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: .0625rem;
  height: .0625rem;
  padding: 0;
  margin: -0.0625rem;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: .125rem solid var(--ring);
  outline-offset: .125rem;
}

/* Improved focus styles */
*:focus-visible {
  outline: .125rem solid var(--ring);
  outline-offset: .125rem;
}

/* Border metal utility */
.border-silver {
  position: relative;
  box-shadow: 0 0.375rem 0.8125rem rgba(0, 0, 0, 0.29),
    0 1.5rem 1.5rem 0rem rgba(0, 0, 0, 0.26),
    0 3.375rem 2rem 0rem rgba(0, 0, 0, 0.15),
    0 6rem 2.375rem 0rem rgba(0, 0, 0, 0.04),
    0 9.375rem 2.625rem 0rem rgba(0, 0, 0, 0.01);
}

.border-silver::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: .0187rem;
  /* background: var(--background); */
  border: .3125rem solid var(--background);
  border-radius: inherit;
  outline: .1062rem solid rgba(194, 194, 194, 1);
  outline-offset: -0.25rem;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.border-silver::after {
  content: '';
  position: absolute;
  inset: .0625rem;
  padding: .0375rem;
  background: linear-gradient(
      90deg,
      var(--foreground) 0%,
      var(--background) 5%,
      var(--foreground) 17%,
      var(--foreground) 83%,
      var(--background) 95%,
      var(--foreground) 100%
    )
    100%;
  border-radius: inherit;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
