# UI Components with CVA and CLSX

This directory contains UI components that use `class-variance-authority` (CVA) and `clsx` for type-safe styling and better developer experience.

## Components

### Button

A type-safe button component with predefined variants and sizes.

```tsx
import { Button } from '@/components/ui';

// Basic usage
<Button>Click me</Button>

// With variants
<Button variant="light">Light Button</Button>
<Button variant="dark">Dark Button</Button>
<Button variant="ghost">Ghost Button</Button>

// With sizes
<Button size="sm">Small Button</Button>
<Button size="md">Medium Button</Button>
<Button size="lg">Large Button</Button>
<Button size="icon">Icon Button</Button>

// With custom classes
<Button className="custom-class">Custom Button</Button>

// Combined
<Button variant="dark" size="lg" className="w-full">
  Large Dark Button
</Button>
```

**Available Variants:**
- `light` - Light theme button
- `dark` - Dark theme button  
- `ghost` - Transparent background button

**Available Sizes:**
- `sm` - Small button
- `md` - Medium button (default)
- `lg` - Large button
- `icon` - Icon-only button

### Input

A type-safe input component with icon support and predefined variants.

```tsx
import { Input } from '@/components/ui';

// Basic usage
<Input value={value} onChange={handleChange} />

// With variants and sizes
<Input 
  variant="light" 
  inputSize="md" 
  value={email} 
  onChange={handleEmailChange}
/>

// With left icon
<Input
  value={password}
  onChange={handlePasswordChange}
  leftIcon={<LockIcon />}
  type="password"
/>

// With custom classes
<Input
  value={search}
  onChange={handleSearchChange}
  className="custom-input"
  containerClassName="custom-container"
/>
```

**Available Variants:**
- `light` - Light theme input (default)

**Available Sizes:**
- `sm` - Small input
- `md` - Medium input (default)
- `lg` - Large input

## Utilities

### cn()

The main utility function for combining classes with `clsx`.

```tsx
import { cn } from '@/lib/utils';

// Basic usage
cn('base-class', 'additional-class')

// Conditional classes
cn('base-class', isActive && 'active-class')

// Multiple conditions
cn(
  'base-class',
  isActive && 'active-class',
  isDisabled && 'disabled-class'
)
```

## Type Safety

All components are fully typed with TypeScript. The variants and sizes are constrained to predefined values, providing compile-time safety.

```tsx
// This will cause a TypeScript error
<Button variant="invalid-variant" /> // ❌

// This is valid
<Button variant="light" /> // ✅
```

## Best Practices

1. **Use the index import** for cleaner imports:
   ```tsx
   import { Button, Input } from '@/components/ui';
   ```

2. **Combine with cn()** for additional classes:
   ```tsx
   <Button className={cn(buttonVariants({ variant: 'dark' }), 'custom-class')}>
   ```

3. **Use TypeScript** for better development experience:
   ```tsx
   import type { ButtonProps } from '@/components/ui';
   ```

## Migration from String Concatenation

Before:
```tsx
className={`btn btn-${variant} btn-${size} ${className}`}
```

After:
```tsx
className={cn(buttonVariants({ variant, size }), className)}
```

This approach provides:
- ✅ Type safety
- ✅ Better IntelliSense
- ✅ Compile-time error checking
- ✅ Cleaner code
- ✅ Better maintainability 