'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import Hunter<PERSON>otLogo from '@/assets/images/logo/logo_white.png';
import BgImg from '@/assets/images/background.png';
import { compassIcon } from '@/assets/images/icons';
import { Button } from '@/components/ui';
import AgeCheck from '@/components/AgeCheck';
import SignIn from '@/components/SignIn';
import SignUp from '@/components/SignUp';
import ForgotPassword from '@/components/ForgotPassword';
import ResetPassword from '@/components/ResetPassword';

const initialState = {
  signIn: false,
  ageCheck: false,
  signUp: false,
  forgotPassword: false,
  resetPassword: false,
};

export default function AgeProtect() {
  const [popUp, setPopUp] = useState(initialState);

  const toggleOnlyPopUp = (key: keyof typeof initialState) => {
    setPopUp((prev) => {
      // If the key is already true, turn all keys to false (close all popups)
      if (prev[key]) {
        return { ...initialState };
      }
      // Otherwise, set only the selected key to true
      return Object.fromEntries(
        Object.keys(initialState).map((k) => [k, k === key])
      ) as typeof initialState;
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center bg-black/70 backdrop-blur-sm">
      {/* Background layer */}
      <div
        className="absolute inset-0 -z-10 bg-cover bg-center opacity-60"
        style={{
          backgroundImage: `url(${BgImg.src})`,
        }}
      />

      {/* Main Card */}
      <div className="relative flex flex-col items-center justify-center w-full max-w-[26rem] mx-auto p-8 mt-32">
        {/* Title */}
        <div className="flex justify-center items-center gap-2">
          <Image
            src={HunterHotLogo}
            alt="HunterHOT Logo"
            height={55}
            priority
          />
          <h1 className="text-2xl sm:text-3xl font-bold mb-1 text-center">
            HunterHOT
          </h1>
        </div>
        {/* Sub-title */}
        <div className="flex justify-center items-center gap-1 mb-6">
          <Image src={compassIcon} alt="Icon" height={35} priority />

          <p className=" font-bold text-center text-base">Unleash The Hunt</p>
        </div>
        {/* Content */}
        <div className="flex flex-col gap-3 items-center px-9 w-full">
          <Button
            // variant={'success'}
            border={true}
            onClick={() => toggleOnlyPopUp('ageCheck')}
          >
            Use Anonymously
          </Button>
          <div className="font-bold">Or</div>
          <Button onClick={() => toggleOnlyPopUp('signIn')}>Sign In</Button>
        </div>
        {/* Links */}
        <div className="flex flex-col items-center gap-1 mt-8 text-xs text-center space-y-1">
          <div className="flex flex-col items-center gap-0.5">
            <div>
              <a href="#about" className="underline font-[600]">
                About HunterHOT
              </a>
            </div>
            <div className="border-[0.05rem] border-white w-16" />
          </div>
          <div>
            By entering, you agree to our{' '}
            <a href="#tos" className="underline font-[700]">
              Terms of Service
            </a>
            ,{' '}
            <a href="#privacy" className="underline font-[700]">
              Privacy Policy
            </a>{' '}
            and{' '}
            <a href="#copyright" className="underline font-[700]">
              Copyright Policy
            </a>
            .
          </div>
          <div className="mt-1 font-[700]">
            You must be over 18 years old to enter
            <br />
            <a href="#2257" className="underline hover:text-white">
              18 U.S.C. § 2257 Statement
            </a>{' '}
            |{' '}
            <a href="#removal" className="underline hover:text-white">
              Content Removal Request
            </a>
          </div>
        </div>
      </div>

      {/* Popups */}
      {popUp.ageCheck ? <AgeCheck toggleOnlyPopUp={toggleOnlyPopUp} /> : null}

      {popUp.signIn ? <SignIn toggleOnlyPopUp={toggleOnlyPopUp} /> : null}

      {popUp.signUp ? <SignUp toggleOnlyPopUp={toggleOnlyPopUp} /> : null}

      {popUp.forgotPassword ? (
        <ForgotPassword toggleOnlyPopUp={toggleOnlyPopUp} />
      ) : null}

      {popUp.resetPassword ? (
        <ResetPassword toggleOnlyPopUp={toggleOnlyPopUp} />
      ) : null}
    </div>
  );
}
