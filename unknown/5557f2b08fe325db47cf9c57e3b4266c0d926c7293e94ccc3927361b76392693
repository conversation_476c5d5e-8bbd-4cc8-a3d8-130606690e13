'use client';

import { Button, Input } from '@/components/ui';
import Image from 'next/image';
import React, { useState } from 'react';
import { closeIcon, emailIcon, passwordIcon } from '@/assets/images/icons';
import { popUpTypes } from '@/types';

type ResetPasswordProps = {
  toggleOnlyPopUp: (key: popUpTypes) => void;
};

export default function ResetPassword({ toggleOnlyPopUp }: ResetPasswordProps) {
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [error] = useState('');

  return (
    <div
      className="fixed inset-0 z-50 min-h-screen w-full bg-white/30 backdrop-blur-sm"
      onClick={() => toggleOnlyPopUp('resetPassword')}
    >
      {/* Modal Card */}
      <div
        onClick={(e) => e.stopPropagation()}
        className="relative bg-white bg-opacity-90 rounded-3xl px-[1.75rem] pt-[2.875rem] pb-[1.8125rem] mx-auto mt-32 w-full max-w-[21rem] flex flex-col items-center justify-center border-[none] text-[1.25rem] [transition:all_0.2s_ease] z-10 font-bold font-['Avenir_Next_Rounded',_sans-serif] [text-shadow:0_.0313rem_.0625rem_rgba(0,_0,_0,_0.12)] whitespace-nowrap border-silver"
      >
        <div className="absolute -top-2 -right-2">
          {/* Close Button */}
          <Button
            variant="light"
            size="icon"
            className="rounded-full hover:bg-gray-200 transition !shadow-[inset_0px_3.78px_3.78px_rgba(0,0,0,0.1),_inset_-7.56px_3.78px_4.73px_rgba(0,0,0,0.05),_0px_0px_2.84px_rgba(0,0,0,1)]"
            onClick={() => toggleOnlyPopUp('resetPassword')}
          >
            <Image src={closeIcon} alt="Close" width={20} height={20} />
          </Button>
        </div>

        <div className="flex flex-col justify-center items-center w-full gap-[1.1875rem]">
          <div className="flex flex-col justify-center items-center gap-1">
            {/* Title */}
            <h2 className="text-2xl !text-dark-foreground font-bold text-center">
              Reset Your Password
            </h2>
            <p className="text-base font-normal text-center text-dark-foreground text-wrap mt-1">
              Please enter the verification code you received in your inbox
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-destructive text-sm text-center font-medium">
              {error}
            </div>
          )}
          {/* Verification Code Input */}
          <div className="w-full">
            <Input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="Verification Code"
              leftIcon={
                <Image
                  src={emailIcon}
                  alt="Verification Code"
                  width={24}
                  height={24}
                />
              }
              className="font-[500] !text-dark-foreground"
              containerClassName=""
              autoComplete="off"
            />
          </div>
          {/* New Password Input */}
          <div className="w-full">
            <Input
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="New Password"
              leftIcon={
                <Image
                  src={passwordIcon}
                  alt="New Password"
                  width={24}
                  height={24}
                />
              }
              className="font-[500] !text-dark-foreground"
              containerClassName=""
              autoComplete="new-password"
            />
          </div>
          {/* Confirm New Password Input */}
          <div className="w-full">
            <Input
              type="password"
              value={confirmNewPassword}
              onChange={(e) => setConfirmNewPassword(e.target.value)}
              placeholder="Confirm New Password"
              leftIcon={
                <Image
                  src={passwordIcon}
                  alt="Confirm New Password"
                  width={24}
                  height={24}
                />
              }
              className="font-[500] !text-dark-foreground"
              containerClassName=""
              autoComplete="new-password"
            />
          </div>
          {/* Reset Button */}
          <Button
            variant="dark"
            disabled={true}
            size="md"
            className="shadow-md"
          >
            Reset
          </Button>
          {/* Links */}
          <div className="w-full flex flex-col items-center text-sm gap-[1.1875rem] text-gray-700 text-wrap">
            <div className="text-center font-[600] text-[.9375rem]">
              Back to{' '}
              <a
                className="font-bold cursor-pointer hover:underline"
                onClick={() => toggleOnlyPopUp('signIn')}
              >
                Sign In
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
