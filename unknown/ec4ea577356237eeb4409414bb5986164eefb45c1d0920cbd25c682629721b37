'use client';

import { motion, AnimatePresence } from 'motion/react';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import HunterHotLogo from '@/assets/images/logo/logo_neon.png';

interface SplashScreenProps {
  onComplete?: () => void;
  duration?: number;
}

export default function SplashScreen({
  onComplete,
  duration = 2000,
}: SplashScreenProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      if (onComplete) {
        setTimeout(onComplete, 800);
      }
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onComplete]);

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0, scale: 3 }}
          transition={{
            duration: 0.6,
            ease: 'easeInOut',
          }}
          className="fixed inset-0 flex items-center justify-center bg-black z-50"
          style={{ willChange: 'transform, opacity' }}
        >
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.5 }}
              transition={{
                duration: 0.6,
                ease: 'easeInOut',
              }}
              className="mb-6"
              style={{ willChange: 'transform, opacity' }}
            >
              <motion.div
                initial={{ filter: 'none' }}
                animate={{
                  filter: [
                    'none',
                    'drop-shadow(0 0 6px rgba(168,85,247,0.8)) drop-shadow(0 0 12px rgba(147,51,234,0.8)) drop-shadow(0 0 24px rgba(126,34,206,1))',
                    'drop-shadow(0 0 8px rgba(168,85,247,0.9)) drop-shadow(0 0 16px rgba(147,51,234,0.9)) drop-shadow(0 0 32px rgba(126,34,206,1))',
                    'drop-shadow(0 0 6px rgba(168,85,247,0.8)) drop-shadow(0 0 12px rgba(147,51,234,0.8)) drop-shadow(0 0 24px rgba(126,34,206,1))',
                  ],
                }}
                transition={{
                  duration: 2,
                  times: [0, 0.3, 0.6, 1],
                  ease: 'easeInOut',
                }}
              >
                <Image
                  src={HunterHotLogo}
                  alt="HunterHOT Logo"
                  width={160}
                  className="mx-auto mix-blend-screen relative z-10"
                  priority
                />
              </motion.div>
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.6,
                delay: 0.2,
                ease: 'easeInOut',
              }}
              className="text-4xl font-bold text-white mb-4 tracking-wider"
              style={{ willChange: 'transform, opacity' }}
            >
              HunterHOT
            </motion.h1>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
