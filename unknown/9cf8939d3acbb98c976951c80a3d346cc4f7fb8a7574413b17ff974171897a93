/* Checkbox lighting effect container */
.checkbox-lighting {
  /* Layout */
  position: relative;
  z-index: 1;

  /* Effects */
  box-shadow: 0 0.125rem 0.5rem 0 rgba(0, 0, 0, 0.08);
  text-shadow: 0 0.0313rem 0.0625rem rgba(0, 0, 0, 0.12);
}

/* Lighting effect - top gradient */
.checkbox-lighting::before {
  /* Content and layout */
  content: '';
  position: absolute;
  inset: 0.3125rem;
  z-index: 1;
  pointer-events: none;
  border-radius: 2.5rem;
  padding: 0.125rem; /* Adjust for border thickness */

  /* Visuals */
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.5) 0%, rgba(255, 255, 255, 0.4) 100%);
  mask-composite: exclude;
  opacity: 0.7;
}

/* Lighting effect - bottom gradient */
.checkbox-lighting::after {
  /* Content and layout */
  content: '';
  position: absolute;
  inset: 0.3125rem;
  z-index: 1;
  pointer-events: none;
  border-radius: 2.5rem;
  padding: 0.0625rem; /* Adjust for border thickness */

  /* Visuals */
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(255, 255, 255, 0.5) 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0.7;
}
