import React from 'react';
import Image from 'next/image';
import Border from '../Border';
import './style.css';
import { checkIcon } from '@/assets/images/icons';

export interface CheckboxProps {
  children: React.ReactNode;
  color?: string;
  direction?: 'left' | 'right';
  checked?: boolean;
  onChange?: (checked: boolean) => void;
}

export default function Checkbox({
  children,
  color = '#00ff00',
  direction = 'left',
  checked,
  onChange,
}: CheckboxProps) {
  const [internalChecked, setInternalChecked] = React.useState(false);
  const isControlled = checked !== undefined;
  const actualChecked = isControlled ? checked : internalChecked;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isControlled) {
      setInternalChecked(e.target.checked);
    }
    onChange?.(e.target.checked);
  };

  return (
    <div
      style={{ backgroundColor: color }}
      className={`checkbox-lighting relative flex cursor-pointer ${
        direction === 'right' ? 'flex-row-reverse' : ''
      } justify-center-safe items-center-safe py-3 px-3 h-16 w-full max-w-2xl min-w-48 rounded-[5rem]`}
      onClick={() => {
        if (!isControlled) {
          setInternalChecked(!actualChecked);
        }
        onChange?.(!actualChecked);
      }}
      role="checkbox"
      aria-checked={actualChecked}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === ' ' || e.key === 'Enter') {
          e.preventDefault();
          if (!isControlled) {
            setInternalChecked(!actualChecked);
          }
          onChange?.(!actualChecked);
        }
      }}
    >
      <div className="flex-grow z-10">{children}</div>
      <div
        className={`relative cursor-pointer z-10 flex justify-center-safe items-center-safe ${
          actualChecked ? 'bg-success' : ''
        } flex-shrink-0 h-full aspect-square rounded-4xl`}
      >
         { actualChecked ? <Image src={checkIcon} alt={"check"} height={20} width={20} /> : null }
        <input
          type="checkbox"
          checked={actualChecked}
          onChange={handleChange}
          style={{
            position: 'absolute',
            opacity: 0,
            width: '100%',
            height: '100%',
            left: 0,
            top: 0,
            margin: 0,
            cursor: 'pointer',
          }}
          tabIndex={-1}
          aria-hidden="true"
        />
        <Border />
      </div>
      <Border />
    </div>
  );
}
