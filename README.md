# HunterHot Frontend

This is the frontend for the HunterHot application, built with Next.js, TypeScript, and Tailwind CSS.

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

- Node.js (v20 or later) [used v22.17.0 for development]
- npm (v10 or later) or yarn (v1.22 or later)

### Installation

1.  **Clone the repository:**

    ```bash
    git clone https://gitlab.openxcell.dev/a-team/hunterhot/hunterhot-frontend.git
    cd hunterhot-frontend
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Set up environment variables:**

    Create a `.env.local` file in the root of the project and add the necessary environment variables.

    ```bash
    NEXT_PUBLIC_API_URL=http://localhost:3001
    ```

4.  **Run the development server:**

    ```bash
    npm run dev
    # or
    yarn dev
    ```

    Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Available Scripts

In the project directory, you can run:

- `npm run dev`: Runs the app in development mode.
- `npm run build`: Builds the app for production.
- `npm run start`: Starts a Next.js production server.
- `npm run lint`: Lints the code using ESLint.

## Project Structure

The project follows a feature-based structure to keep the code organized and maintainable.

```
src/
├── app/          # Next.js App Router pages
├── assets/       # Static assets like images and fonts
├── components/   # Shared components (UI, layouts, forms)
├── features/     # Feature-based modules (e.g., auth, profile)
├── hooks/        # Custom React hooks
├── lib/          # Utility functions
├── services/     # API service calls
├── store/        # State management (e.g., Zustand, Redux)
├── styles/       # Global styles
└── types/        # TypeScript types and interfaces
```

## Technologies Used

- [Next.js](https://nextjs.org/) - v15.3.4
- [React](https://react.dev/) - v19.0.0
- [TypeScript](https://www.typescriptlang.org/) - v5
- [Tailwind CSS](https://tailwindcss.com/) - v4
- [ESLint](https://eslint.org/) - v9
- [Prettier](https://prettier.io/) - Code formatting

## Linting and Formatting

This project uses ESLint for linting and Prettier for code formatting. It's recommended to set up your editor to automatically format on save.

To manually run the linter, use:

```bash
npm run lint
```

## Deployment

The application can be deployed using Docker. A `Dockerfile` is included in the root of the project.

The `.gitlab-ci.yml` file is configured for CI/CD with GitLab.
