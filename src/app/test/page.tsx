'use client';

// import { Button } from '@/components/ui';
import { Button, Checkbox, Input } from '@/components/ui';
import SelectChip from '@/components/ui/SelectChip';
// import Checkbox from '@/components/ui/Checkbox'
import React from 'react';
// import PrimaryPopup from '@/components/ui/PrimaryPopup';
// import Image from 'next/image';
// import { closePurpleIcon } from '@/assets/images/icons';
// import Select from '@/components/ui/Select';
// import SelectChip from '@/components/ui/SelectChip';

export default function Test() {
  // const [open, setOpen] = useState(false);

  // const options = [
  //   { value: 'option1', label: 'Option 1' },
  //   { value: 'option2', label: 'Option 2' },
  //   { value: 'option3', label: 'Option 3', disabled: true },
  // ];

  return (
    <div className="min-h-screen min-w-full flex flex-wrap items-center justify-center bg-white px-12 gap-6">
      <div className="h-full w-fit flex flex-col items-center justify-center bg-white p-12 gap-6 rounded-3xl border-4 border-dashed border-black">
        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">Light Variant</h2>
          <Button variant="light" border={true} borderVariant="silver">
            Light + Silver Border
          </Button>
          {/* <Button variant="light" border={true} borderVariant="purple">
          Light + Purple Border
        </Button>
        <Button variant="light" border={true} borderVariant="purple-dark">
          Light + Purple Dark Border
        </Button> */}
        </div>

        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">Dark Variant</h2>
          <Button variant="dark" border={true} borderVariant="silver">
            Dark + Silver Border
          </Button>
          {/* <Button variant="dark" border={true} borderVariant="purple">
          Dark + Purple Border
        </Button>
        <Button variant="dark" border={true} borderVariant="purple-dark">
          Dark + Purple Dark Border
        </Button> */}
        </div>

        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">Primary Variant</h2>
          <Button variant="primary" border={true} borderVariant="silver">
            Primary + Silver Border
          </Button>
          <Button variant="primary" border={true} borderVariant="purple">
            Primary + Purple Border
          </Button>
          <Button variant="primary" border={true} borderVariant="purple-dark">
            Primary + Purple Dark Border
          </Button>
        </div>

        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">
            Primary Dark Variant
          </h2>
          {/* <Button variant="primary-dark" border={true} borderVariant="silver">
          Primary Dark + Silver Border
        </Button>
        <Button variant="primary-dark" border={true} borderVariant="purple">
          Primary Dark + Purple Border
        </Button> */}
          <Button
            variant="primary-dark"
            border={true}
            borderVariant="purple-dark"
          >
            Primary Dark + Purple Dark Border
          </Button>
        </div>

        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">Ghost Variant</h2>
          {/* <Button variant="ghost" border={true} borderVariant="silver">
          Ghost + Silver Border
        </Button>
        <Button variant="ghost" border={true} borderVariant="purple">
          Ghost + Purple Border
        </Button> */}
          <Button variant="ghost" border={true} borderVariant="purple-dark">
            Ghost + Purple Dark Border
          </Button>
        </div>
      </div>

      <div className="h-full w-fit flex flex-col items-center justify-center bg-white p-12 gap-6 rounded-3xl border-4 border-dashed border-black">
        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">
            Select Chip Variant
          </h2>
          <SelectChip>
            <span className="w-full h-full flex justify-center">Checked</span>
          </SelectChip>
        </div>

        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">
            Checkbox Variant
          </h2>
          <Checkbox color="#ff0000">
            <span className="w-full h-full flex justify-center">Checked</span>
          </Checkbox>
        </div>
      </div>

      <div className="h-full w-fit flex flex-col items-center justify-center bg-white p-12 gap-6 rounded-3xl border-4 border-dashed border-black">
        <div className="flex flex-col gap-4 items-center">
          <h2 className="text-xl text-black font-bold mb-2">Input Variant</h2>
          <Input
            type="text"
            value=""
            error="This is an error33333333333333333333333 333333333333333333333333333333 3333333333333333333"
            helperText="This is helper text33333333333333333333333333333333333333333333333333333  "
            onChange={() => {
              console.log('changed');
            }}
            placeholder="Input"
            border={true}
          />
        </div>
      </div>
    </div>
  );
}
