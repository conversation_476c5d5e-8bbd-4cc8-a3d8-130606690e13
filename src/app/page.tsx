'use client';

import { useState } from 'react';
import SplashScreen from '@/components/SplashScreen';
import Border from '@/components/ui/Border';

export default function Home() {
  const [showContent, setShowContent] = useState(false);

  return (
    <>
      <SplashScreen onComplete={() => setShowContent(true)} />
      {showContent && (
        <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
          <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
            <h1 className="text-9xl text-white font-bold ">HunterHOT</h1>
          </main>
        </div>
      )}
    </>
  );
}
