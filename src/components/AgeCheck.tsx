'use client';

import React from 'react';
import { Button } from '@/components/ui';
import Image from 'next/image';
import { cakeIcon } from '@/assets/images/icons';
import Age18 from '@/assets/images/age-18.png';
import { popUpTypes } from '@/types';

type AgeCheckProps = {
  toggleOnlyPopUp: (key: popUpTypes) => void;
};

export default function AgeCheck({ toggleOnlyPopUp }: AgeCheckProps) {
  return (
    <div
      onClick={() => toggleOnlyPopUp('ageCheck')}
      className="fixed inset-0 z-50 min-h-screen w-full bg-white/60 backdrop-blur-sm"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="relative bg-white bg-opacity-90 rounded-3xl px-[2.25rem] pb-4 pt-8.5 mx-auto mt-32 w-full max-w-[19rem] flex flex-col items-center justify-center border-[none] text-[1.25rem] [transition:all_0.2s_ease] z-10 font-bold font-['Avenir_Next_Rounded',_sans-serif] [text-shadow:0_0.5px_1px_rgba(0,_0,_0,_0.12)] whitespace-nowrap border-silver"
      >
        {/* 18+ Badge */}
        <div className="absolute -top-10 -right-8 z-20 flex items-center justify-center w-32">
          <Image src={Age18} alt={'age'} className="w-full h-auto" />
        </div>
        <div className="w-full flex flex-col whitespace-normal text-left z-1">
          {/* Cake Icon */}
          <div className="mb-2 h-[4.2412rem] w-auto">
            <Image src={cakeIcon} alt={'cake img'} className="h-full w-auto" />
          </div>
          {/* Title */}
          <h2 className="text-[2.5rem]/[3.0625rem] font-bold !text-dark-foreground mb-4">
            Age Check
          </h2>
          {/* Description */}
          <p className="text-[#070519] text-[1.1875rem]/[1.4rem] font-[600]">
            HunterHOT contains adult content,{' '}
            <span className="font-[500]">so we need to verify your age.</span>
          </p>
          {/* Buttons */}
          <div className="flex flex-col gap-4 mt-4 mb-6 w-full">
            <Button variant="dark">Yes, I am 18+</Button>
            <Button variant="dark">No, I am under 18</Button>
          </div>
          {/* Terms */}
          <p className="text-[0.813rem]/[1rem] text-dark-foreground font-[500] text-left">
            By Clicking, you confirm you&apos;re over 18 and agree to our{' '}
            <a
              href="https://www.google.com"
              className="underline font-[600] cursor-pointer"
            >
              Terms of Service
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
