/* Border element base styles */
/*
  Stacking order:
  - .border-el: z-index -2 (bottom layer)
  - Button pseudo-elements (::before, ::after): z-index -1
  - Button content: z-index auto (default, above both)
*/
.border-el {
  /* Layout */
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: inherit;

  /* Effects */
  z-index: -1;
}

/* First rectangle border */
.rec-1 {
  /* Box model */
  box-sizing: border-box;

  /* Visuals */
  border: 0.2306rem solid rgba(166, 166, 166, 1);
}

/* Dark Purple variant for first rectangle border */
.rec-1-purple {
  /* Visuals */
  background: rgba(16, 19, 34, 1);
  outline: 0.6px solid transparent;

  /* Effects */
  box-shadow: inset 0 0 0 0.6px #262948, inset 0 -0.3px 0 0.6px #4b458f,
    inset 0 -0.6px 0 0.6px #5c63ae;
}

/* Green variant for first rectangle border */
.rec-1-green {
  border: 2.67px solid rgba(54, 200, 25, 1);
  /* border: 2.67px solid red; */
}

/* Second rectangle border with shadow */
.rec-2 {
  /* Box model */
  width: calc(100% - 0.2188rem);
  height: calc(100% - 0.2188rem);

  /* Visuals */
  border: 0.0925rem solid rgba(237, 237, 237, 1);

  /* Effects */
  box-shadow: 0 0 0.1875rem 0 rgba(0, 0, 0, 1);
}

/* Dark Purple variant for second rectangle border with shadow */
.rec-2-purple {
  /* Box model */
  width: calc(100% - 0.1094rem);
  height: calc(100% - 0.1094rem);

  /* Visuals */
  background: rgba(16, 4, 53, 1);
  border: 0.6px solid rgba(181, 128, 254, 1);

  /* Effects */
  box-shadow: 0px -1px 4px 0px rgba(87, 37, 179, 1),
    inset -1px 0px 100px 0px rgba(87, 37, 179, 1);
}

/* Green variant for second rectangle border with shadow */
.rec-2-green {
  /* Box model */
  width: calc(100% - 0.05rem);
  height: calc(100% - 0.05rem);

  /* visuals */
  border: 1.07px solid rgba(255, 255, 255, 1);
}

/* Third rectangle border */
.rec-3 {
  /* Box model */
  width: calc(100% - 0.0313rem);
  height: calc(100% - 0.0313rem);

  /* Visuals */
  border: 0.0925rem solid rgba(242, 242, 242, 1);
}

/* Dark Purple variant for third rectangle border */
.rec-3-purple {
  /* Box model */
  width: calc(100% - 0.1094rem);
  height: calc(100% - 0.1094rem);

  /* Visuals */
  background: linear-gradient(
    180deg,
    #4e30a0 0%,
    rgba(255, 255, 255, 0.3) 15.01%,
    rgba(28, 17, 58, 0) 30.02%,
    rgba(181, 188, 255, 0.3) 49.07%,
    rgba(28, 17, 58, 0) 65.03%,
    rgba(255, 255, 255, 0.2) 85.05%,
    rgba(28, 17, 58, 0) 100.05%
  );
}

/* Green variant for third rectangle border */
.rec-3-green {
  /* Box model */
  width: calc(100% - 0.1185rem);
  height: calc(100% - 0.0904rem);
}

.rec-3-green::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 1.07px;

  background: linear-gradient(270deg, #d6ffd8 0%, #00ff0a 100%);
  border-radius: inherit;

  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Fourth rectangle border */
.rec-4 {
  /* Box model */
  width: calc(100% - 0.125rem);
  height: calc(100% - 0.125rem);
  box-sizing: border-box;

  /* Visuals */
  border: 0.0462rem solid rgba(94, 94, 94, 0.5);
}

/* Dark Purple variant for fourth rectangle border (hidden) */
.rec-4-purple {
  display: none;
}

/* Green variant for fourth rectangle border */
.rec-4-green {
  /* Box model */
  width: calc(100% - 0.1185rem);
  height: calc(100% - 0.0904rem);

  /* visuals */
  border: 0.53px solid rgba(0, 97, 10, 1);
}

/* Fifth rectangle border with gradient and blend mode */
.rec-5 {
  /* Box model */
  width: calc(100% - 0.2rem);
  height: calc(100% - 0.2rem);

  /* Visuals */
  border: 0.05rem solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(
        to top,
        #4a4a4a 0%,
        #999999 8%,
        #f0f0f0 15%,
        #e7e7e7 16%,
        #797979 17%,
        #000000 18%,
        #585858 87%,
        #bababa 88%,
        #e1e1e1 89%,
        #ececec 90%,
        #f3f3f3 91%,
        #f5f5f5 93%,
        #4a4a4a 100%
      )
      border-box;

  /* Effects */
  mix-blend-mode: darken;
}

/* Green variant for fifth rectangle border with gradient and blend mode */
.rec-5-green {
  /* Box model */
  width: calc(100% - 0.1185rem);
  height: calc(100% - 0.0904rem);
}

.rec-5-green::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.25px;

  background: linear-gradient(180deg, #2ac420 0%, #cbffee 100%);
  border-radius: inherit;

  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Inner outline for border effect */
.inner {
  /* Visuals */
  outline: 0.0418rem solid #000;
  outline-offset: -0.2813rem;
}

/* Purple variant for inner outline */
.inner-purple {
  background: linear-gradient(180deg, #0a031a 0%, #46216e 100%);
  opacity: 0.1;

  outline: 0.0418rem solid #000;
  outline-offset: -0.2813rem;
}

/* Purple variant for inner outline */
.inner-purple-dark {
  /* Box model */
  width: calc(100% - 0.4375rem);
  height: calc(100% - 0.4375rem);
  box-sizing: border-box;

  /* Visuals */
  background: rgba(18, 0, 30, 1);
  position: relative;

  /* Effects */
  box-shadow: 0px 1.42px 1.42px 0px rgba(0, 0, 0, 0.8) inset;
}

.inner-purple-dark::before {
  /* Content and layout */
  content: '';
  position: absolute;
  top: -0.68px;
  left: -0.68px;
  right: -0.68px;
  bottom: -0.68px;
  padding: 0.68px;

  /* Visuals */
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(181, 128, 254, 1) 100%
  );
  border-radius: inherit;

  /* Effects */
  mix-blend-mode: darken;
}

/* Green variant for inner outline */
.inner-green {
  /* Box Model */
  width: calc(100% - .2188rem);
  height: calc(100% - 0.3617rem);

  /* background: linear-gradient(180deg, #004300 0%, #37ca1a 100%); */
  border: 0.07px solid rgba(4, 100, 4, 1);
}
