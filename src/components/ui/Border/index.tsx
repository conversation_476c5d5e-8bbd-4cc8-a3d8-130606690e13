import React from 'react';
import './style.css';

export type VARIANT = 'silver' | 'purple' | 'green' | 'purple-dark';

export interface BorderProps {
  className?: string;
  variant?: VARIANT;
}

function getClass(variant: VARIANT) {
  const defaultClass = {
    rec1Class: 'border-el rec-1',
    rec2Class: 'border-el rec-2',
    rec3Class: 'border-el rec-3',
    rec4Class: 'border-el rec-4',
    rec5Class: 'border-el rec-5',
    innerClass: 'border-el inner',
  };

  switch (variant) {
    case 'silver':
      return defaultClass;

    case 'purple':
      return { ...defaultClass, innerClass: 'border-el inner-purple' };

    case 'green':
      return {
        ...defaultClass,
        rec1Class: 'border-el rec-1-green',
        rec2Class: 'border-el rec-2-green',
        rec3Class: 'border-el rec-3-green',
        rec4Class: 'border-el rec-4-green',
        rec5Class: 'border-el rec-5-green',
        innerClass: 'border-el inner-green',
      };

    case 'purple-dark':
      return {
        ...defaultClass,
        rec1Class: 'border-el rec-1-purple',
        rec2Class: 'border-el rec-2-purple',
        rec3Class: 'border-el rec-3-purple',
        rec4Class: 'border-el rec-4-purple',
        innerClass: 'border-el inner-purple-dark',
      };

    default:
      return defaultClass;
  }
}

export default function Border({
  className = '',
  variant = 'silver',
}: BorderProps) {
  const borderClass = getClass(variant);

  return (
    <span
      style={{
        borderRadius: 'inherit',
      }}
      className={`absolute left-0 top-0 flex justify-center items-center w-full h-full ${
        variant === 'purple' ? 'mix-blend-color-dodge !bg-black' : ''
      } ${className}`}
    >
      <span className={borderClass?.rec1Class} />
      <span className={borderClass?.rec2Class} />
      <span className={borderClass?.rec3Class} />
      <span className={borderClass?.rec4Class} />
      <span className={borderClass?.rec5Class} />
      <span className={borderClass?.innerClass} />
    </span>
  );
}
