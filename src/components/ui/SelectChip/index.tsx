import React from 'react';
import { cn } from '@/lib/utils';
import Border from '../Border';
import './style.css';

export interface SelectChipProps {
  children: React.ReactNode;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
  borderClass?: string;
  disabled?: boolean;
}

/**
 * SelectChip visually matches <PERSON><PERSON> (purple/success) and toggles like Checkbox.
 * No variant prop: default is purple, toggled is success.
 */
export default function SelectChip({
  children,
  checked,
  onChange,
  className,
  borderClass,
  disabled = false,
}: SelectChipProps) {
  const [internalChecked, setInternalChecked] = React.useState(false);
  const isControlled = checked !== undefined;
  const actualChecked = isControlled ? checked : internalChecked;

  // Toggle handler
  const handleToggle = () => {
    if (disabled) return;
    if (!isControlled) setInternalChecked(!actualChecked);
    onChange?.(!actualChecked);
  };

  // Keyboard accessibility
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (disabled) return;
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      handleToggle();
    }
  };

  return (
    <div
      className={cn(
        'selectchip-base',
        actualChecked ? 'selectchip-success' : 'selectchip-purple',
        disabled && 'selectchip-disabled',
        className
      )}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-pressed={actualChecked}
      aria-disabled={disabled}
      onClick={handleToggle}
      onKeyDown={handleKeyDown}
    >
      <span className="selectchip-content">{children}</span>
      <Border
        variant={actualChecked ? 'green' : 'purple'}
        className={borderClass}
      />
    </div>
  );
}
