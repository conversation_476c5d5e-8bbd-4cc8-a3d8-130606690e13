/* SelectChip base styles (match But<PERSON>) */
.selectchip-base {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;
  padding: 0 2rem;
  border: none;
  border-radius: 2rem;
  font-family: 'Avenir Next Rounded', sans-serif;
  font-size: 1.125rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  min-height: 3rem;
  min-width: 3rem;
  user-select: none;
  outline: none;
} 

.selectchip-base:active {
  transform: scale(0.98);
}

.selectchip-base:focus-visible {
  outline: 0.0625rem solid var(--foreground);
  outline-offset: 0.1875rem;
}

.selectchip-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Purple (default) variant */
.selectchip-purple {
  background: linear-gradient(
    180deg,
    rgba(30, 9, 78, 1) 0%,
    /* rgba(186, 177, 207, 1) 10%, */ rgba(30, 9, 78, 1) 12%,
    rgba(30, 9, 78, 1) 88%,
    /* rgba(186, 177, 207, 1) 90%, */ rgba(30, 9, 78, 1) 100%
  );
  color: var(--foreground);
}

.selectchip-purple::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  right: 0.125rem;
  bottom: 0.125rem;
  border-radius: calc(2rem - 0.0938rem);
  z-index: -1;
  background: linear-gradient(
    180deg,
    rgba(10, 3, 50, 1) 0%,
    rgba(75, 48, 145, 1) 100%
  );
  box-shadow: 0 0 0 0.0313rem rgba(233, 233, 233, 0.8),
    0 0 0 0.0625rem rgba(206, 206, 206, 0.15),
    inset 0 -0.25rem 0.25rem rgba(0, 0, 0, 0.1),
    inset -0.5rem 0.25rem 0.3125rem rgba(0, 0, 0, 0.05);
}

.selectchip-purple::after {
  content: '';
  position: absolute;
  top: 0.375rem;
  left: 0.375rem;
  right: 0.375rem;
  height: 1.1rem;
  border-radius: 2rem 2rem 0 0;
  z-index: -1;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.01) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

/* Success (toggled) variant */
.selectchip-success {
  background: linear-gradient(180deg, #004300 0%, #37ca1a 100%);
  color: var(--foreground);
}

.selectchip-success::before {
  content: '';
  position: absolute;
  top: 0.3rem;
  left: 0.3rem;
  right: 0.3rem;
  height: 37.5%;
  /* width: calc(100% - .405rem); */
  border-radius: 2rem 2rem 0 0;
  z-index: -1;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.01) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

/* Content wrapper for children */
.selectchip-content {
  position: relative;
  z-index: 2;
  width: 100%;
  text-align: center;
}
