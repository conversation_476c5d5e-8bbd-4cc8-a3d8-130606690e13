import React, { useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import './style.css';
import Border from '../Border';

export const inputVariants = cva(
  'ip', // base classes
  {
    variants: {
      variant: {
        light: 'input-light',
      },
      inputSize: {
        sm: 'input-sm',
        md: 'input-md',
        lg: 'input-lg',
      },
    },
    defaultVariants: {
      variant: 'light',
      inputSize: 'md',
    },
  }
);

const inputContainerVariants = cva(
  'input-container', // base classes
  {
    variants: {
      hasLeftIcon: {
        true: '',
        false: '',
      },
      hasRightIcon: {
        true: '',
        false: '',
      },
    },
    defaultVariants: {
      hasLeftIcon: false,
      hasRightIcon: false,
    },
  }
);

export interface InputProps<T = string>
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'value'>,
    VariantProps<typeof inputVariants> {
  border?: boolean;
  value: T extends string | number | readonly string[] ? T : string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerClassName?: string;
}

const InputComponent = <T = string,>(
  {
    variant,
    border = true,
    inputSize,
    value,
    error,
    helperText,
    leftIcon,
    rightIcon,
    className,
    containerClassName,
    ...props
  }: InputProps<T>,
  ref: React.Ref<HTMLInputElement>
) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!value ? false : true);

  return (
    <div className="relative flex flex-col gap-1 min-w-3xs max-w-50">
      <div
        className={cn(
          inputContainerVariants({
            hasLeftIcon: !!leftIcon,
            hasRightIcon: !!rightIcon,
          }),
          containerClassName
        )}
      >
        {leftIcon && <span className="input-left-icon">{leftIcon}</span>}
        <input
          className={cn(
            inputVariants({ variant, inputSize }),
            'z-10',
            className
          )}
          value={value}
          ref={ref}
          {...props}
        />
        {rightIcon && <span className="input-right-icon">{rightIcon}</span>}
        {border && <Border />}
      </div>
      <div className="w-full h-full flex justify-between items-start gap-2 text-[0.625rem]">
        {error && (
          <span className="w-full text-red-500 flex flex-wrap break-all">
            {error}
          </span>
        )}
        {helperText && (
          <span className="w-full text-gray-500 flex justify-end-safe flex-wrap break-all">
            {helperText}
          </span>
        )}
      </div>
    </div>
  );
};

const Input = React.forwardRef(InputComponent) as <T = string>(
  props: InputProps<T> & { ref?: React.Ref<HTMLInputElement> }
) => React.ReactElement;

Object.defineProperty(Input, 'displayName', { value: 'Input' });

export default Input;
