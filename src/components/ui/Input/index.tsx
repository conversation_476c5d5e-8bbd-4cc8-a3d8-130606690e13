import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import './style.css';
import Border from '../Border';

export const inputVariants = cva(
  'ip', // base classes
  {
    variants: {
      variant: {
        light: 'input-light',
      },
      inputSize: {
        sm: 'input-sm',
        md: 'input-md',
        lg: 'input-lg',
      },
    },
    defaultVariants: {
      variant: 'light',
      inputSize: 'md',
    },
  }
);

const inputContainerVariants = cva(
  'input-container', // base classes
  {
    variants: {
      hasIcon: {
        true: '',
        false: '',
      },
    },
    defaultVariants: {
      hasIcon: false,
    },
  }
);

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  border?: boolean;
  value: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      variant,
      border = true,
      inputSize,
      value,
      error,
      helperText,
      leftIcon,
      rightIcon,
      className,
      containerClassName,
      ...props
    },
    ref
  ) => {
    return (
      <div className="relative flex flex-col gap-1 min-w-3xs max-w-50">
        <div
          className={cn(
            inputContainerVariants({ hasIcon: !!leftIcon }),
            containerClassName
          )}
        >
          {leftIcon && <span className="input-left-icon">{leftIcon}</span>}
          <input
            className={cn(
              inputVariants({ variant, inputSize }),
              'z-10',
              className
            )}
            value={value}
            ref={ref}
            {...props}
          />
          {rightIcon && <span className="input-right-icon">{rightIcon}</span>}
          {border && <Border />}
        </div>
        <div className="w-full h-full flex justify-between items-start gap-2 text-[0.625rem]">
          {error && (
            <span className="w-full text-red-500 flex flex-wrap break-all">
              {error}
            </span>
          )}
          {helperText && (
            <span className="w-full text-gray-500 flex justify-end-safe flex-wrap break-all">
              {helperText}
            </span>
          )}
        </div>
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
