/* Input container with gradient background and shadow */
.input-container {
  /* Layout */
  display: flex;
  align-items: center;
  position: relative;

  /* Box model */
  height: fit-content;
  width: 100%;
  padding: 0.5rem 1.5rem;
  border-radius: 1rem;

  /* Visuals */
  background: linear-gradient(180deg, #fff 0%, #e2e2e2 50%, #fff 100%);

  /* Effects */
  box-shadow: 
    inset 0 -0.25rem 0.25rem 0 rgba(0, 0, 0, 0.1),
    inset -0.5rem 0.25rem 0.3125rem rgba(0, 0, 0, 0.05),
    0 0 0.1875rem rgba(0, 0, 0, 1);

  z-index: 0;
}

/* Left icon inside input */
.input-left-icon {
  /* Layout */
  display: flex;
  align-items: center;
  margin-right: 1rem;

  /* Typography */
  font-size: 2rem;
}

.input-right-icon {
  /* Layout */
  display: flex;
  align-items: center;
  margin-left: 1rem;

  /* Typography */
  font-size: 2rem;
}

/* Light input style */
.input-light {
  /* Visuals */
  background: transparent;
  color: #000;
}

.input-light:disabled {
  /* Visuals */
  background: transparent;
  color: #888;
}

.ip {
  z-index: 10;
}

/* Remove outline on focus for .ip class */
.ip:focus-visible {
  /* Effects */
  outline: none;
}

/* Medium input size */
.input-md {
  /* Box model */
  width: 100%;
  height: 2.3125rem;
}
