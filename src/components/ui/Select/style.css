/* Select Base Styles */
.select {
  position: relative;
  display: inline-block;
  width: 100%;
  cursor: pointer;
  outline: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.select:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.select-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.select-value {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1f2937;
  font-weight: 500;
}

.select-placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.select-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.select-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.select-clear:hover {
  background: #374151;
}

.select-chevron {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.select-chevron img {
  transition: opacity 0.2s ease;
}

.select-chevron-open {
  transform: rotate(180deg);
}

/* Size Variants */
.select-sm .select-content {
  padding: 6px 8px;
  font-size: 14px;
  min-height: 32px;
}

.select-md .select-content {
  padding: 8px 12px;
  font-size: 16px;
  min-height: 40px;
}

.select-lg .select-content {
  padding: 12px 16px;
  font-size: 18px;
  min-height: 48px;
}

/* Variant Styles */
.select-default .select-content {
  background: #ffffff;
  /* border: 1px solid #e5e7eb; */
}

.select-default:hover .select-content {
  border-color: #d1d5db;
}

.select-default.select-open .select-content {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select-outline .select-content {
  background: transparent;
  /* border: 2px solid #e5e7eb; */
}

.select-outline:hover .select-content {
  border-color: #d1d5db;
}

.select-outline.select-open .select-content {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select-ghost .select-content {
  background: transparent;
  border: 1px solid transparent;
}

.select-ghost:hover .select-content {
  background: #f9fafb;
  border-color: #e5e7eb;
}

.select-ghost.select-open .select-content {
  background: #ffffff;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Disabled State */
.select-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.select-disabled .select-content {
  background: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
}

.select-disabled:hover .select-content {
  border-color: #e5e7eb;
}

/* Dropdown Styles */
.select-dropdown {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  max-height: 240px;
  overflow-y: auto;
}

.select-search-container {
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.select-search {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.select-search:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.select-options {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
}

.select-option {
  padding: 8px 12px;
  cursor: pointer;
  color: #1f2937;
  font-size: 14px;
  transition: background-color 0.2s ease;
  border: none;
  text-align: left;
  width: 100%;
}

.select-option:hover,
.select-option-focused {
  background: #f3f4f6;
}

.select-option-selected {
  background: #eff6ff;
  color: #1d4ed8;
  font-weight: 500;
}

.select-option-selected:hover,
.select-option-selected.select-option-focused {
  background: #dbeafe;
}

.select-option-disabled {
  cursor: not-allowed;
  color: #9ca3af;
  opacity: 0.6;
}

.select-option-disabled:hover {
  background: transparent;
}

.select-loading,
.select-no-results {
  padding: 12px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  cursor: default;
}

.select-loading:hover,
.select-no-results:hover {
  background: transparent;
}

/* Scrollbar Styling */
.select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.select-dropdown::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.select-dropdown::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive Design */
@media (max-width: 640px) {
  .select-dropdown {
    max-height: 200px;
  }
  
  .select-option {
    padding: 12px 16px;
    font-size: 16px;
  }
}
