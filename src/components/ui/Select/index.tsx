import React, { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import './style.css';
import Border from '../Border';
import Image from 'next/image';
import { downArrowIcon, upArrowIcon } from '@/assets/images/icons';

export interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

export const selectVariants = cva(
  'select', // base classes
  {
    variants: {
      variant: {
        default: 'select-default',
        outline: 'select-outline',
        ghost: 'select-ghost',
      },
      size: {
        sm: 'select-sm',
        md: 'select-md',
        lg: 'select-lg',
      },
      state: {
        default: '',
        disabled: 'select-disabled',
        open: 'select-open',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      state: 'default',
    },
  }
);

export interface SelectProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'>,
    VariantProps<typeof selectVariants> {
  options?: Option[];
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  searchable?: boolean;
  loading?: boolean;
  border?: boolean;
  showArrow?: boolean;
  containerClassName?: string;
  loadOptions?: (query: string) => Promise<Option[]>;
  onChange?: (value: string | null) => void;
  onSearchChange?: (query: string) => void;
}

const Select = React.forwardRef<HTMLDivElement, SelectProps>(
  (
    {
      options = [],
      value,
      defaultValue,
      placeholder = 'Select an option...',
      disabled = false,
      clearable = false,
      searchable = false,
      loading = false,
      border = true,
      showArrow = true,
      variant,
      size,
      className,
      containerClassName,
      loadOptions,
      onChange,
      onSearchChange,
      ...props
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(value || defaultValue || '');
    const [searchQuery, setSearchQuery] = useState('');
    const [focusedIndex, setFocusedIndex] = useState(-1);
    const [asyncOptions, setAsyncOptions] = useState<Option[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
    
    const triggerRef = useRef<HTMLDivElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const listRef = useRef<HTMLUListElement>(null);

    const currentOptions = loadOptions ? asyncOptions : options;
    const filteredOptions = searchable && searchQuery
      ? currentOptions.filter(option =>
          option.label.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : currentOptions;

    const selectedOption = currentOptions.find(option => option.value === selectedValue);

    // Handle async options loading
    const loadAsyncOptions = useCallback(async (query: string) => {
      if (!loadOptions) return;
      
      setIsLoading(true);
      try {
        const results = await loadOptions(query);
        setAsyncOptions(results);
      } catch (error) {
        console.error('Error loading options:', error);
        setAsyncOptions([]);
      } finally {
        setIsLoading(false);
      }
    }, [loadOptions]);

    // Debounced search
    useEffect(() => {
      if (!loadOptions || !isOpen) return;
      
      const timeout = setTimeout(() => {
        loadAsyncOptions(searchQuery);
      }, 300);

      return () => clearTimeout(timeout);
    }, [searchQuery, loadOptions, isOpen, loadAsyncOptions]);

    // Update position when dropdown opens
    useEffect(() => {
      if (isOpen && triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + window.scrollY,
          left: rect.left + window.scrollX,
          width: rect.width,
        });
      }
    }, [isOpen]);

    // Handle click outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          triggerRef.current &&
          !triggerRef.current.contains(event.target as Node) &&
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false);
          setSearchQuery('');
          setFocusedIndex(-1);
        }
      };

      if (isOpen) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
      }
    }, [isOpen]);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (disabled) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          if (!isOpen) {
            setIsOpen(true);
            if (loadOptions) loadAsyncOptions('');
          } else {
            setFocusedIndex(prev => 
              prev < filteredOptions.length - 1 ? prev + 1 : 0
            );
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          if (isOpen) {
            setFocusedIndex(prev => 
              prev > 0 ? prev - 1 : filteredOptions.length - 1
            );
          }
          break;
        case 'Enter':
          e.preventDefault();
          if (!isOpen) {
            setIsOpen(true);
            if (loadOptions) loadAsyncOptions('');
          } else if (focusedIndex >= 0) {
            handleSelect(filteredOptions[focusedIndex].value);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setSearchQuery('');
          setFocusedIndex(-1);
          break;
        case 'Tab':
          setIsOpen(false);
          setSearchQuery('');
          setFocusedIndex(-1);
          break;
      }
    };

    const handleSelect = (optionValue: string) => {
      setSelectedValue(optionValue);
      setIsOpen(false);
      setSearchQuery('');
      setFocusedIndex(-1);
      onChange?.(optionValue);
    };

    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedValue('');
      setSearchQuery('');
      setIsOpen(false);
      setFocusedIndex(-1);
      onChange?.(null);

      console.log('Selection cleared');
      
      
      // Focus the trigger after clearing
      setTimeout(() => {
        if (triggerRef.current) {
          triggerRef.current.focus();
        }
      }, 0);
    };

    const handleTriggerClick = () => {
      if (disabled) return;
      
      setIsOpen(!isOpen);
      if (!isOpen && loadOptions) {
        loadAsyncOptions('');
      }
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const query = e.target.value;
      setSearchQuery(query);
      setFocusedIndex(-1);
      onSearchChange?.(query);
    };

    // Focus management
    useEffect(() => {
      if (isOpen && searchable && searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, [isOpen, searchable]);

    // Scroll focused option into view
    useEffect(() => {
      if (focusedIndex >= 0 && listRef.current) {
        const focusedElement = listRef.current.children[focusedIndex] as HTMLElement;
        if (focusedElement) {
          focusedElement.scrollIntoView({ block: 'nearest' });
        }
      }
    }, [focusedIndex]);

    const dropdownContent = (
      <motion.div
        ref={dropdownRef}
        className="select-dropdown"
        style={{
          position: 'absolute',
          top: dropdownPosition.top,
          left: dropdownPosition.left,
          width: dropdownPosition.width,
          zIndex: 1000,
        }}
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
      >
        {searchable && (
          <div className="select-search-container">
            <input
              ref={searchInputRef}
              type="text"
              className="select-search"
              placeholder="Search options..."
              value={searchQuery}
              onChange={handleSearchChange}
              onKeyDown={handleKeyDown}
            />
          </div>
        )}
        
        <ul
          ref={listRef}
          className="select-options"
          role="listbox"
          aria-label="Select options"
        >
          {isLoading || loading ? (
            <li className="select-option select-loading" role="option">
              Loading...
            </li>
          ) : filteredOptions.length === 0 ? (
            <li className="select-option select-no-results" role="option">
              No options found
            </li>
          ) : (
            filteredOptions.map((option, index) => (
              <li
                key={option.value}
                className={cn(
                  'select-option',
                  {
                    'select-option-selected': option.value === selectedValue,
                    'select-option-focused': index === focusedIndex,
                    'select-option-disabled': option.disabled,
                  }
                )}
                role="option"
                aria-selected={option.value === selectedValue}
                aria-disabled={option.disabled}
                onClick={() => !option.disabled && handleSelect(option.value)}
                onMouseEnter={() => setFocusedIndex(index)}
              >
                {option.label}
              </li>
            ))
          )}
        </ul>
      </motion.div>
    );

    return (
      <div
        className={cn(containerClassName)}
        ref={ref}
        {...props}
      >
        <div
          ref={triggerRef}
          className={cn(
            selectVariants({
              variant,
              size,
              state: disabled ? 'disabled' : isOpen ? 'open' : 'default',
            }),
            'z-10',
            className
          )}
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-controls="select-dropdown"
          aria-activedescendant={
            focusedIndex >= 0 ? `option-${focusedIndex}` : undefined
          }
          tabIndex={disabled ? -1 : 0}
          onClick={handleTriggerClick}
          onKeyDown={handleKeyDown}
        >
          <div className="select-content">
            <span className={cn(
              'select-value',
              { 'select-placeholder': !selectedOption }
            )}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
            
            <div className="select-actions">
              {clearable && selectedValue && !disabled && (
                <button
                  className="select-clear"
                  onClick={handleClear}
                  tabIndex={-1}
                  aria-label="Clear selection"
                >
                  ×
                </button>
              )}
              {showArrow && (
                <div className="select-chevron" aria-hidden="true">
                  <Image
                    src={isOpen ? upArrowIcon : downArrowIcon}
                    alt=""
                    width={16}
                    height={16}
                  />
                </div>
              )}
            </div>
          </div>
          
          {border && <Border />}
        </div>

        {typeof document !== 'undefined' && createPortal(
          <AnimatePresence>
            {isOpen && dropdownContent}
          </AnimatePresence>,
          document.body
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
