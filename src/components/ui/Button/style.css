/*
  Stacking order for button and border:
  - .border-el: z-index -2 (bottom layer)
  - Button pseudo-elements (::before, ::after): z-index -1
  - Button content: z-index auto (default, above both)
*/
/* Base button styles */
.btn {
  /* Layout */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;

  /* Box model */
  padding: 0 5rem;
  border: none;
  border-radius: 5rem;

  /* Typography */
  font-family: 'Avenir Next Rounded', sans-serif;
  font-size: 1.25rem;
  font-weight: 500;

  /* Effects */
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  text-shadow: 0 0.0313rem 0.0625rem rgba(0, 0, 0, 0.12);
}

.btn:active {
  /* Effects */
  transform: scale(0.98);
}

.btn:focus-visible {
  /* Effects */
  outline: 0.0625rem solid var(--foreground);
  outline-offset: 0.1875rem;
}

.btn:disabled {
  /* Effects */
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button shadow variants */
.btn-light,
.btn-dark {
  /* Effects */
  /* z-index: inherit; */
  box-shadow: inset 0 -0.25rem 0.25rem 0 rgba(0, 0, 0, 0.1),
    inset -0.5rem 0.25rem 0.3125rem rgba(0, 0, 0, 0.05),
    inset 0 0.0625rem 0.425rem 0.2rem rgba(0, 0, 0, 1),
    0 0.25rem 0.25rem rgba(0, 0, 0, 0.29),
    0 0.4375rem 0.4375rem 0 rgba(0, 0, 0, 0.26),
    0 1.0625rem 0.625rem 0 rgba(0, 0, 0, 0.15),
    0 1.8125rem 0.75rem 0 rgba(0, 0, 0, 0.04),
    0 0.8125rem 0.8125rem 0 rgba(0, 0, 0, 0.01);
}

.btn-light {
  /* Visuals */
  background: linear-gradient(
    180deg,
    var(--foreground) 0%,
    rgba(226, 226, 226, 1) 50%,
    var(--foreground) 100%
  );
  color: var(--background);
}

.btn-dark {
  /* Visuals */
  background: linear-gradient(
    180deg,
    var(--background) 0%,
    rgba(63, 63, 63, 1) 80%,
    rgb(96, 96, 96) 100%
  );
  color: var(--foreground);
}

/* Primary variant - Main button component */
.btn-primary {
  /* Visuals */
  background: linear-gradient(
    180deg,
    rgba(30, 9, 78, 1) 0%,
    /* rgba(186, 177, 207, 1) 10%, */
    rgba(30, 9, 78, 1) 12%,
    rgba(30, 9, 78, 1) 88%,
    /* rgba(186, 177, 207, 1) 90%, */
    rgba(30, 9, 78, 1) 100%
  );
  color: var(--foreground);
  border-radius: 2rem;
  padding: 0.875rem 2rem;
  font-size: 1.125rem;
  font-weight: 500;

  /* Effects */
  /* box-shadow: 
    0 0 0 0.0313rem rgba(233, 233, 233, 0.6),
    0 0 0 0.0625rem rgba(206, 206, 206, 0.3),
    0 0 0 0.0938rem rgba(166, 166, 166, 0.8),
    0 0 0.1875rem rgba(0, 0, 0, 0.2),
    inset 0 -0.25rem 0.25rem rgba(0, 0, 0, 0.1),
    inset -0.5rem 0.25rem 0.3125rem rgba(0, 0, 0, 0.05); */
}

/* Inner gradient layer using ::before */
.btn-primary::before {
  /* Content and layout */
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  right: 0.125rem;
  bottom: 0.125rem;
  border-radius: calc(2rem - 0.0938rem);
  z-index: -1;

  /* Visuals */
  background: linear-gradient(
    180deg,
    rgba(10, 3, 50, 1) 0%,
    rgba(75, 48, 145, 1) 100%
  );

  /* Effects */
  box-shadow: 0 0 0 0.0313rem rgba(233, 233, 233, 0.8),
    0 0 0 0.0625rem rgba(206, 206, 206, 0.15),
    inset 0 -0.25rem 0.25rem rgba(0, 0, 0, 0.1),
    inset -0.5rem 0.25rem 0.3125rem rgba(0, 0, 0, 0.05);
}

/* Top highlight and shine effect using ::after */
.btn-primary::after {
  /* Content and layout */
  content: '';
  position: absolute;
  top: 0.375rem;
  left: 0.375rem;
  right: 0.375rem;
  height: 1.375rem;
  border-radius: 2rem 2rem 0 0;
  z-index: -1;

  /* Visuals */
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.01) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );

  /* Effects */
  mix-blend-mode: screen;
}

.btn-primary-dark {
  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.98),
    -1px 1px 2px 0px rgba(0, 0, 0, 0.85), -3px 3px 2px 0px rgba(0, 0, 0, 0.5),
    -5px 5px 3px 0px rgba(0, 0, 0, 0.15), -8px 8px 3px 0px rgba(0, 0, 0, 0.02);
}

/* Success variant - Green button */
.btn-success {
  /* Visuals */
  background: linear-gradient(
    180deg,
    rgba(0, 67, 0, 1) 0%,
    rgba(214, 255, 216, 1) 10%,
    rgba(0, 67, 0, 1) 12%,
    rgba(0, 67, 0, 1) 88%,
    rgba(214, 255, 216, 1) 90%,
    rgba(0, 67, 0, 1) 100%
  );
  color: var(--foreground);
  border-radius: 2rem;
  padding: 0.875rem 2rem;
  font-size: 1.125rem;
  font-weight: 500;

  /* Effects */
  box-shadow: 0 0 0 0.0625rem rgba(50, 190, 24, 0.6),
    0 0 0 0.0625rem rgba(255, 255, 255, 0.6),
    0 0 0 0.0625rem rgba(54, 199, 26, 0.3), 0 0 0 0.0938rem rgba(0, 97, 10, 0.8),
    0 0 0.1875rem rgba(0, 0, 0, 0.2), inset 0 -4px 4px rgba(0, 0, 0, 0.1),
    inset -0.5rem 0.25rem 0.3125rem rgba(0, 0, 0, 0.05);
}

/* Add more variants and sizes as needed, following the above format */
.btn-icon {
  height: fit-content;
  width: fit-content;
  padding: 1rem;
}

.btn-sm {
  height: 2.75rem;
  width: 100%;
}

.btn-md {
  height: 3.4564rem;
  width: 100%;
}

.btn-lg {
  height: 3.75rem;
  width: 100%;
}