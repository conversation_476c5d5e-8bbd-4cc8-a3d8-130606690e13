import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import './style.css';
import Border, { VARIANT } from '../Border';

export const buttonVariants = cva(
  'btn', // base classes
  {
    variants: {
      variant: {
        light: 'btn-light',
        dark: 'btn-dark',
        primary: 'btn-primary',
        'primary-dark': 'btn-primary-dark',
        // success: 'btn-success',
        ghost: 'btn-ghost',
      },
      size: {
        sm: 'btn-sm',
        md: 'btn-md',
        lg: 'btn-lg',
        icon: 'btn-icon',
      },
    },
    defaultVariants: {
      variant: 'light',
      size: 'md',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  children: React.ReactNode;
  border?: boolean;
  borderVariant?: VARIANT;
}

export default function Button({
  children,
  variant,
  size,
  border = true,
  borderVariant = 'silver',
  className,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size }), className)}
      {...props}
    >
      {children}
      {border && <Border variant={borderVariant} />}
    </button>
  );
}
