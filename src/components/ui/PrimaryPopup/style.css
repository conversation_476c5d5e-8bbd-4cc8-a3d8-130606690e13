.primary-popup-overlay {
  --radius: 2.125rem;
  position: fixed;
  inset: 0;
  z-index: 1000;
  animation: popup-fade-in 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(7, 7, 19, 0.9);
}

@keyframes popup-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.container-border-outer {

  position: relative;

  padding: 0.25rem;

  background: #101322;

  border-radius: var(--radius);
}

.container-border-outer::after {
  content: '';
  position: absolute;
  top: -0.6px;
  left: -0.6px;
  right: -0.6px;
  bottom: -0.6px;
  background: linear-gradient(
    360deg,
    #262948 -30.14%,
    #4b458f 34.31%,
    #5c63ae 100%
  );
  border-radius: var(--radius);
  z-index: -1;
}
/* 
.container-border-outer::before {
  content:"";
  position:absolute;
  width:15px;
  height:15px;
  right: 32px;
  top: 0;
  transform: translate(-50%, -50%) rotate(45deg);
  border: 0.6px solid #5c63ae;
  border-top-left-radius: 1.5px;
  background: #101322;
  clip-path: polygon(0 0, 100% 0%, 0% 100%, 0% 100%);
} */

.container-border-inner {
  padding: .5625rem 0rem;

  background: #100435;

  border: 0.6px solid #b580fe;

  border-radius: inherit;

  box-shadow: 0px -1px 4px 0px #5725b3, inset -1px 0px 16px 0px #5725b3;

}

/* .container-border-inner::before {
  content:"";
  position:absolute;
  width:15px;
  height:15px;
  right: 42px;
  top: 0.0125rem;
  transform: translate(20%, -20%) rotate(45deg);
  border: 0.6px solid #b580fe;
  border-top-left-radius: 1.5px;
  background: #100435;
  clip-path: polygon(0 0, 100% 0%, 0% 100%, 0% 100%);
  box-shadow: 0px -1px 4px 0px #5725b3, inset -1px 0px 6px 0px rgb(87, 37, 179), inset -1px 0px 50px 0px rgb(87, 37, 179, 0.1);
} */

.border-line {
  padding: 0.0625rem;

  border-radius: inherit;

  background: linear-gradient(
    180deg,
    #4e30a0 0%,
    rgba(255, 255, 255, 0.6) 5.29%,
    #1c113a 13.46%,
    #b5bcff 49.04%,
    #1c113a 84.14%,
    #ffffff 92.79%,
    #1c113a 100%
  );
}

.content-border {
  background: #070713;

  border: 0.6px solid #4e30a0;

  border-radius: inherit;

  box-shadow: 0px -2px 3px 0px #9e7cf7;
}

.main-content {
  padding: 2.0625rem 0.75rem;

  background: #12001e;

  border: 1px solid #12001e;

  border-radius: inherit;

  box-shadow: inset 0px -2px 20px 0px #413469;
}

.popup-close-btn {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 2rem;
  cursor: pointer;
  z-index: 10;
  line-height: 1;
  padding: 0;
  transition: color 0.2s;
}
.popup-close-btn:hover {
  color: #b580fe;
}
