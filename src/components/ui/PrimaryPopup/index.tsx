import React from 'react';
import './style.css';
import Button from '../Button';
import Image from 'next/image';
import { closePurpleIcon } from '@/assets/images/icons';

interface PrimaryPopupProps {
  children: React.ReactNode;
  closable?: boolean;
  onClose?: () => void;
  open?: boolean;
}

export default function PrimaryPopup({
  children,
  closable = true,
  onClose,
  open = false,
}: PrimaryPopupProps) {
  if (!open) return null;
  // Handler for overlay click
  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!closable) return;
    console.log('hello', e.target, e.currentTarget);

    // Only close if the click is on the overlay, not inside the popup
    if (e.target === e.currentTarget && onClose) {
      onClose();
    }
  };

  return (
    <div className="primary-popup-overlay" onClick={handleOverlayClick}>
      <div className="absolute flex justify-center items-center w-fit h-fit">
        <div
          className="container-border-outer"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="container-border-inner">
            <div className="border-line">
              <div className="content-border">
                {closable && (
                  <div className="absolute -top-2 -right-2">
                    <Button
                      variant="primary-dark"
                      size="icon"
                      border={true}
                      className="!rounded-full"
                      borderVariant="purple-dark"
                      onClick={onClose}
                      aria-label="Close popup"
                      type="button"
                    >
                      <Image
                        src={closePurpleIcon}
                        alt="close"
                        width={18}
                        height={18}
                        style={{
                          filter:
                            'drop-shadow(0px 0px 9px rgba(132, 66, 255, 1))',
                        }}
                      />
                    </Button>
                  </div>
                )}
                <div className="main-content">{children}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
